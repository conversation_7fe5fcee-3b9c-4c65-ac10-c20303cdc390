import { getWarehouseOverview } from '../../services';
import RepositoryInfo from './RepositoryInfo';
import { checkGitHubRepoExists } from '../../services/githubService';
import { DocsBody, DocsDescription, DocsPage, DocsTitle } from 'fumadocs-ui/page';
import RenderMarkdown from '@/app/components/renderMarkdown';
import { getMDXComponents } from '@/components/mdx-components';
import FloatingChat from '@/app/chat';

// 服务器组件，处理数据获取
export default async function RepositoryPage({ params, searchParams }: any) {
  try {
    const owner = params.owner;
    const name = params.name;
    // 从查询参数中获取分支信息
    const branch = searchParams.branch as string | undefined;

    if (!owner || !name) {
      throw new Error('Missing owner or repository name');
    }

    // 在服务器端获取数据
    const response = await getWarehouseOverview(owner, name, branch);

    // 如果获取数据失败，尝试从GitHub获取仓库信息
    if (!response.success || !response.data) {
      // 检查GitHub仓库是否存在
      const githubRepoExists = await checkGitHubRepoExists(owner, name, branch);

      // 如果GitHub仓库存在，则显示GitHub仓库信息
      if (githubRepoExists) {
        return (
          <RepositoryInfo
            owner={owner}
            branch={branch}
            name={name}
          />
        );
      } else {
        return (
          <RepositoryInfo
            owner={owner}
            branch={branch}
            name={name}
          />
        );
      }
    }

    const compiled = await RenderMarkdown({
      markdown: response.data.content,
    });
    const MdxContent = compiled!.body;

    const { title, description } = compiled!.frontmatter as any;

    // 将数据传递给客户端组件进行渲染
    return (
      <DocsPage toc={compiled!.toc}>
        <DocsTitle>{title ?? ""}</DocsTitle>
        <DocsDescription>{description ?? ""}</DocsDescription>
        <>
          <DocsBody>
            <MdxContent
              components={getMDXComponents({
              })}
            />
          </DocsBody>
          <FloatingChat
            appId={`builtin_${owner}_${name}`}
            organizationName={owner}
            repositoryName={name}
            title={`${name} AI 助手`}
            theme="light"
            enableDomainValidation={false}
            embedded={false}
            onError={(error) => {
              console.error('Built-in chat error:', error);
            }}
          />
        </>
      </DocsPage>
    );
  } catch (error) {
    console.error('Repository page error:', error);

    return (
      <DocsPage>
        <DocsTitle>页面加载失败</DocsTitle>
        <DocsBody>
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-destructive mb-2">
                无法加载仓库页面
              </h2>
              <p className="text-muted-foreground mb-4">
                仓库 <code className="bg-muted px-2 py-1 rounded">{params.owner}/{params.name}</code> 可能不存在或暂时无法访问
              </p>
              <p className="text-sm text-muted-foreground mb-6">
                错误详情: {error instanceof Error ? error.message : '未知错误'}
              </p>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                重新加载
              </button>
              <a
                href={`/${params.owner}`}
                className="px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md transition-colors"
              >
                返回 {params.owner}
              </a>
              <a
                href="/"
                className="px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md transition-colors"
              >
                返回首页
              </a>
            </div>
          </div>
        </DocsBody>
      </DocsPage>
    );
  }
}
