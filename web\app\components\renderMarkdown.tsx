import { compileMDX } from "@fumadocs/mdx-remote";
import remarkGfm from "remark-gfm";
import { rehypeCode } from 'fumadocs-core/mdx-plugins';
import { remarkStructure } from 'fumadocs-core/mdx-plugins';
import { remarkImage } from 'fumadocs-core/mdx-plugins';

export default async function RenderMarkdown(props: { markdown: string }) {
    if (props.markdown) {

        // 先处理mermaid图表
        props.markdown = props.markdown.replace(/```mermaid\s*(\n?)([\s\S]*?)```/g, (match, p1, p2) => {
            return `<Mermaid chart={\`${p2}\`}/>`;
        });

        // mdx中{}需要被替换为`{`和`}`字符串
        // 只替换不在JSX标签内的花括号，避免破坏JSX语法
        props.markdown = props.markdown.replace(/(?<!<[^>]*)\{(?![^<]*>)/g, '`{`').replace(/(?<!<[^>]*)\}(?![^<]*>)/g, '`}`');

        const compiled = await compileMDX({
            source: props.markdown,
            mdxOptions: {
                remarkPlugins: [
                    remarkGfm,
                    remarkStructure,
                    remarkImage,
                ],
                rehypePlugins: [rehypeCode],
            },
        });

        return compiled;
    }
    return null;

}